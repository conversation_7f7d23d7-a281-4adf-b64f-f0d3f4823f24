#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
切面可视化组件
显示X或Y方向的切面图和量测结果
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt

from .data_model import DataModel

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CrossSectionCanvas(QWidget):
    """
    切面可视化组件
    
    功能：
    1. 显示X或Y方向的切面图
    2. 叠加量测结果（阈值线、边界线、宽度标注）
    3. 显示当前柱子信息
    """
    
    def __init__(self, data_model: DataModel, direction: str):
        super().__init__()
        self.data_model = data_model
        self.direction = direction  # "X" 或 "Y"
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(6, 6))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        
        layout.addWidget(self.canvas)
        
        # 初始化显示
        self._show_empty_plot()
    
    def _connect_signals(self):
        """连接数据模型信号"""
        self.data_model.current_pillar_changed.connect(self._update_plot)
        self.data_model.measurement_updated.connect(self._update_plot)
    
    def _show_empty_plot(self):
        """显示空白图"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, f'请选择柱子\n查看{self.direction}方向切面',
                    transform=self.ax.transAxes,
                    ha='center', va='center',
                    fontsize=14, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()
    
    def _update_plot(self):
        """更新图形显示"""
        if not self.data_model.has_current_pillar():
            self._show_empty_plot()
            return
        
        current_measurement = self.data_model.current_measurement
        if not current_measurement:
            self._show_empty_plot()
            return
        
        # 清除之前的图
        self.ax.clear()
        
        # 获取数据
        pillar = self.data_model.current_pillar
        calibrated_matrix = self.data_model.calibrated_matrix
        pillar_id = pillar['pillar_id']
        
        # 提取切面数据
        center_row, center_col = pillar['center']
        bbox = pillar['bbox']
        min_row, min_col, max_row, max_col = bbox

        # 获取步长
        x_step = self.data_model.x_step
        y_step = self.data_model.y_step

        if self.direction == "X":
            # X方向切面（通过中心行）
            slice_data = calibrated_matrix[center_row, min_col:max_col + 1]
            coords_pixel = np.arange(min_col, max_col + 1, dtype=float)
            coords = coords_pixel * x_step  # 转换为物理坐标

            # 量测数据
            max_height = current_measurement['x_max_height']
            top_height = current_measurement['x_top_height']
            bottom_height = current_measurement['x_bottom_height']
            top_border = (current_measurement['x_top_border'][0] * x_step,
                         current_measurement['x_top_border'][1] * x_step)
            bottom_border = (current_measurement['x_bottom_border'][0] * x_step,
                           current_measurement['x_bottom_border'][1] * x_step)
            top_width = current_measurement['width_x_top']
            bottom_width = current_measurement['width_x_bottom']

            x_label = "X方向位置 (μm)"

        else:  # Y方向
            # Y方向切面（通过中心列）
            slice_data = calibrated_matrix[min_row:max_row + 1, center_col]
            coords_pixel = np.arange(min_row, max_row + 1, dtype=float)
            coords = coords_pixel * y_step  # 转换为物理坐标

            # 量测数据
            max_height = current_measurement['y_max_height']
            top_height = current_measurement['y_top_height']
            bottom_height = current_measurement['y_bottom_height']
            top_border = (current_measurement['y_top_border'][0] * y_step,
                         current_measurement['y_top_border'][1] * y_step)
            bottom_border = (current_measurement['y_bottom_border'][0] * y_step,
                           current_measurement['y_bottom_border'][1] * y_step)
            top_width = current_measurement['width_y_top']
            bottom_width = current_measurement['width_y_bottom']

            x_label = "Y方向位置 (μm)"
        
        # 绘制切面曲线
        self.ax.plot(coords, slice_data, 'b-', linewidth=2, label='高度曲线')
        
        # 绘制最大高度线
        self.ax.axhline(y=max_height, color='black', linestyle='--', 
                       alpha=0.7, label=f'最大高度 ({max_height:.2f} μm)')
        
        # 绘制顶部阈值线和边界
        self.ax.axhline(y=top_height, color='red', linestyle='-', 
                       alpha=0.7, label=f'顶部阈值 ({top_height:.2f} μm)')
        
        top_left, top_right = top_border
        self.ax.axvline(x=top_left, color='red', linestyle=':', alpha=0.8)
        self.ax.axvline(x=top_right, color='red', linestyle=':', alpha=0.8)
        
        # 顶部宽度标注
        self.ax.annotate('', xy=(top_right, top_height),
                        xytext=(top_left, top_height),
                        arrowprops=dict(arrowstyle='<->', color='red', lw=2))
        
        mid_top = (top_left + top_right) / 2
        self.ax.text(mid_top, top_height + (max_height - top_height) * 0.3,
                    f'顶部宽度: {top_width:.2f} μm',
                    ha='center', va='bottom', fontsize=10, 
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 绘制底部阈值线和边界
        self.ax.axhline(y=bottom_height, color='orange', linestyle='-', 
                       alpha=0.7, label=f'底部阈值 ({bottom_height:.2f} μm)')
        
        bottom_left, bottom_right = bottom_border
        self.ax.axvline(x=bottom_left, color='orange', linestyle=':', alpha=0.8)
        self.ax.axvline(x=bottom_right, color='orange', linestyle=':', alpha=0.8)
        
        # 底部宽度标注
        self.ax.annotate('', xy=(bottom_right, bottom_height),
                        xytext=(bottom_left, bottom_height),
                        arrowprops=dict(arrowstyle='<->', color='orange', lw=2))
        
        mid_bottom = (bottom_left + bottom_right) / 2
        min_height = np.min(slice_data)
        self.ax.text(mid_bottom, bottom_height - (bottom_height - min_height) * 0.3,
                    f'底部宽度: {bottom_width:.2f} μm',
                    ha='center', va='top', fontsize=10,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 设置标签和标题
        self.ax.set_xlabel(x_label)
        self.ax.set_ylabel('高度 (μm)')
        self.ax.set_title(f'柱子 {pillar_id} - {self.direction}方向切面')
        
        # 添加图例
        # 暂不需要
        # self.ax.legend(loc='upper right', fontsize=9)
        
        # 设置网格
        self.ax.grid(True, alpha=0.3)
        
        # 调整布局并刷新
        self.figure.tight_layout()
        self.canvas.draw()
