#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口 - 简洁的界面布局
遵循Linus原则：简单布局，清晰职责分离
"""

import os
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QSplitter, QMessageBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QKeyEvent

from .data_model import DataModel
from .calibration_dialog import CalibrationDialog
from .pillar_detection_canvas import PillarDetectionCanvas
from .cross_section_canvas import CrossSectionCanvas


class MainWindow(QMainWindow):
    """
    主窗口类
    
    Linus式设计：
    1. 简洁布局 - 只有必要的UI元素
    2. 拖拽优先 - 主要交互方式
    3. 键盘友好 - 支持方向键切换
    """
    
    def __init__(self):
        super().__init__()
        
        # 数据模型
        self.data_model = DataModel()
        
        # 初始化UI
        self._setup_ui()
        self._connect_signals()
        
        # 窗口设置
        self.setWindowTitle("DAZ自动测量系统")
        self.setMinimumSize(1000, 800)
        self.resize(1200, 1000)
        
        # 启用拖拽
        self.setAcceptDrops(True)
    
    def _setup_ui(self):
        """设置UI布局"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # === 顶部控制区域 ===
        top_layout = QHBoxLayout()
        
        # 文件路径标签
        self.file_label = QLabel("请拖入DAZ文件...")
        self.file_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 2px dashed #ccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #666;
            }
        """)
        self.file_label.setMinimumHeight(50)
        
        # 找平按钮
        self.calibration_button = QPushButton("查看找平结果")
        self.calibration_button.setEnabled(False)
        self.calibration_button.setMaximumWidth(120)
        
        # 最小柱高设置
        height_layout = QHBoxLayout()
        height_layout.addWidget(QLabel("最小柱高(μm):"))
        self.height_spinbox = QDoubleSpinBox()
        self.height_spinbox.setRange(0.0, 100.0)
        self.height_spinbox.setDecimals(1)
        self.height_spinbox.setSingleStep(0.1)
        self.height_spinbox.setValue(1.0)
        self.height_spinbox.setSuffix(" μm")
        self.height_spinbox.setMaximumWidth(90)
        # 关键：不让SpinBox获取焦点，避免阻止键盘事件
        self.height_spinbox.setFocusPolicy(Qt.ClickFocus)
        height_layout.addWidget(self.height_spinbox)
        
        top_layout.addWidget(self.file_label, 1)
        top_layout.addLayout(height_layout)
        top_layout.addWidget(self.calibration_button)
        
        main_layout.addLayout(top_layout)

        # === 可视化区域 ===
        # 使用QSplitter实现可调整大小的三栏布局
        splitter = QSplitter(Qt.Horizontal)

        # 左栏：柱子检测可视化
        self.pillar_canvas = PillarDetectionCanvas(self.data_model)
        splitter.addWidget(self.pillar_canvas)

        # 右侧：切面图区域（包含信息标签）
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 5, 5, 5)

        # 柱子信息标签（仅在切面图上方）
        self.pillar_info_label = QLabel("")
        self.pillar_info_label.setAlignment(Qt.AlignCenter)
        self.pillar_info_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1976d2;
            }
        """)
        self.pillar_info_label.setMaximumHeight(40)
        right_layout.addWidget(self.pillar_info_label)

        # 切面图区域
        section_splitter = QSplitter(Qt.Horizontal)

        # X方向切面
        self.x_section_canvas = CrossSectionCanvas(self.data_model, "X")
        section_splitter.addWidget(self.x_section_canvas)

        # Y方向切面
        self.y_section_canvas = CrossSectionCanvas(self.data_model, "Y")
        section_splitter.addWidget(self.y_section_canvas)

        right_layout.addWidget(section_splitter)
        splitter.addWidget(right_widget)
        
        # 设置初始比例 (1:2) - 左侧柱子检测，右侧切面图区域
        splitter.setSizes([200, 600])
        
        main_layout.addWidget(splitter, 1)
        
        # === 状态栏 ===
        self.status_label = QLabel("就绪")
        self.statusBar().addWidget(self.status_label)
    
    def _connect_signals(self):
        """连接信号槽"""
        # 数据模型信号
        self.data_model.file_loaded.connect(self._on_file_loaded)
        self.data_model.plane_calibrated.connect(self._on_plane_calibrated)
        self.data_model.pillars_detected.connect(self._on_pillars_detected)
        self.data_model.current_pillar_changed.connect(self._on_current_pillar_changed)
        self.data_model.error_occurred.connect(self._on_error)
        
        # UI控件信号
        self.calibration_button.clicked.connect(self._show_calibration_dialog)
        self.height_spinbox.valueChanged.connect(self._on_height_changed)
    
    # === 拖拽事件处理 ===
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1:
                file_path = urls[0].toLocalFile()
                if file_path.lower().endswith(('.daz', '.da6')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            self.data_model.load_file(file_path)
            event.acceptProposedAction()
    
    # === 键盘事件处理 ===
    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件 - 支持方向键切换柱子"""
        if self.data_model.is_pillars_detected():
            if event.key() == Qt.Key_Left:
                self.data_model.previous_pillar()
                return
            elif event.key() == Qt.Key_Right:
                self.data_model.next_pillar()
                return
            elif event.key() == Qt.Key_Up:
                self.data_model.previous_pillar()
                return
            elif event.key() == Qt.Key_Down:
                self.data_model.next_pillar()
                return

        super().keyPressEvent(event)
    
    # === 信号槽处理 ===
    def _on_file_loaded(self, file_path: str):
        """文件加载完成"""
        filename = os.path.basename(file_path)
        self.file_label.setText(f"已加载: {filename}")
        self.file_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #4CAF50;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #2E7D32;
            }
        """)
        self.status_label.setText("文件解析中...")
    
    def _on_plane_calibrated(self):
        """平面校准完成"""
        self.calibration_button.setEnabled(True)
        self.status_label.setText("平面校准完成")
    
    def _on_pillars_detected(self):
        """柱子检测完成"""
        count = self.data_model.pillar_count
        self.status_label.setText(f"检测到 {count} 个柱子")
    
    def _on_current_pillar_changed(self, pillar_index: int):
        """当前柱子变化"""
        pillar = self.data_model.current_pillar
        if pillar:
            pillar_id = pillar['pillar_id']
            total = self.data_model.pillar_count
            self.pillar_info_label.setText(f"柱子 {pillar_id} (第{pillar_index+1}个/共{total}个)")
            self.status_label.setText(f"正在量测柱子 {pillar_id}")
        else:
            self.pillar_info_label.setText("")
    
    def _on_error(self, error_msg: str):
        """错误处理"""
        self.status_label.setText(f"错误: {error_msg}")
        QMessageBox.warning(self, "错误", error_msg)
    
    def _show_calibration_dialog(self):
        """显示校准对话框"""
        if self.data_model.is_plane_calibrated():
            dialog = CalibrationDialog(self.data_model, self)
            dialog.exec()
    
    def _on_height_changed(self, value: float):
        """最小柱高变化"""
        self.data_model.set_min_pillar_height(value)
